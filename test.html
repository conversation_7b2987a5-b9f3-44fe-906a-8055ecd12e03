<!DOCTYPE html>
<html>
<head>
    <title>GPT-4o-mini流式输出演示</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #response {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            min-height: 100px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>GPT-4o-mini流式输出演示</h1>
        <div id="response">正在思考...</div>
        
        <script src="https://js.puter.com/v2/"></script>
        <script>
            async function streamResponse() {
                const responseDiv = document.getElementById('response');
                responseDiv.textContent = "";
                
                try {
                    const response = await puter.ai.chat(
                        "请用中文写一篇关于人工智能未来发展的短文，大约300字",
                        {
                            model: 'openai/gpt-4o-mini',
                            stream: true  // 启用流式输出
                        }
                    );
                    
                    for await (const part of response) {
                        if (part?.text) {
                            responseDiv.textContent += part.text;
                        }
                    }
                } catch (error) {
                    responseDiv.textContent = "错误: " + error.message;
                }
            }
            
            // 启动流式响应
            streamResponse();
        </script>
    </div>
</body>
</html>